<template>
  <section class="featured-section">
    <div class="section-container">
      <h2 class="section-title">精选非遗项目</h2>
      <div class="heritage-grid">
        <div class="heritage-card" v-for="item in featuredHeritage" :key="item.id">
          <img :src="item.image" :alt="item.name" />
          <div class="heritage-info">
            <h3>{{ item.name }}</h3>
            <p>{{ item.description }}</p>
            <router-link :to="`/heritage/${item.id}`" class="btn-detail">
              查看详情
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref } from 'vue'

const featuredHeritage = ref([
  {
    id: 1,
    name: '景泰蓝制作技艺',
    description: '北京传统金属工艺，国家级非物质文化遗产',
    image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=200&fit=crop&crop=center'
  },
  {
    id: 2,
    name: '苏绣',
    description: '江苏传统刺绣工艺，四大名绣之一',
    image: 'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=300&h=200&fit=crop&crop=center'
  },
  {
    id: 3,
    name: '京剧',
    description: '中国国粹，世界级非物质文化遗产',
    image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=200&fit=crop&crop=center'
  }
])
</script>

<style scoped>
.featured-section {
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80px 0;
  box-sizing: border-box;
}

.section-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-title {
  font-size: 3rem;
  text-align: center;
  margin-bottom: 60px;
  color: white;
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.heritage-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 30px;
  max-height: 60vh;
  overflow-y: auto;
}

.heritage-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.heritage-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.3);
}

.heritage-card img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.heritage-info {
  padding: 25px;
}

.heritage-info h3 {
  font-size: 1.4rem;
  margin-bottom: 12px;
  color: #2c3e50;
  font-weight: 600;
}

.heritage-info p {
  color: #666;
  margin-bottom: 20px;
  line-height: 1.6;
  font-size: 0.95rem;
}

.btn-detail {
  display: inline-block;
  padding: 12px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-decoration: none;
  border-radius: 25px;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-detail:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .featured-section {
    padding: 60px 0;
  }

  .section-title {
    font-size: 2.5rem;
    margin-bottom: 40px;
  }

  .heritage-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    max-height: 65vh;
  }

  .heritage-card img {
    height: 180px;
  }

  .heritage-info {
    padding: 20px;
  }

  .heritage-info h3 {
    font-size: 1.3rem;
  }
}

@media (max-width: 480px) {
  .section-container {
    padding: 0 15px;
  }

  .section-title {
    font-size: 2rem;
  }

  .heritage-grid {
    grid-template-columns: 1fr;
    max-height: 70vh;
  }

  .heritage-card img {
    height: 160px;
  }
}
</style>