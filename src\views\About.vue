<template>
  <div class="about-page">
    <!-- 导航栏 -->
    <Navbar />

    <!-- 主要内容 -->
    <main class="main-content">
      <!-- 英雄区域 -->
      <section class="hero-section">
        <div class="hero-content">
          <h1 class="hero-title">关于我们</h1>
          <p class="hero-subtitle">传承千年文化，守护非遗之美</p>
          <div class="hero-decoration"></div>
        </div>
      </section>

      <!-- 使命愿景 -->
      <section class="mission-section">
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">我们的使命</h2>
            <p class="section-subtitle">致力于非物质文化遗产的保护、传承与创新发展</p>
          </div>

          <div class="mission-grid">
            <div class="mission-card">
              <div class="mission-icon">
                <i class="icon-protect">🛡️</i>
              </div>
              <h3>保护传承</h3>
              <p>深入挖掘和记录非遗文化的精髓，运用现代技术手段进行数字化保护，确保珍贵文化遗产得以完整传承。</p>
            </div>

            <div class="mission-card">
              <div class="mission-icon">
                <i class="icon-innovate">💡</i>
              </div>
              <h3>创新发展</h3>
              <p>在尊重传统的基础上，探索非遗文化与现代生活的融合方式，让古老的智慧在新时代焕发生机。</p>
            </div>

            <div class="mission-card">
              <div class="mission-icon">
                <i class="icon-share">🌍</i>
              </div>
              <h3>文化传播</h3>
              <p>通过多元化的展示平台和互动体验，让更多人了解、欣赏和参与非遗文化的传承与发展。</p>
            </div>
          </div>
        </div>
      </section>

      <!-- 团队介绍 -->
      <section class="team-section">
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">核心团队</h2>
            <p class="section-subtitle">汇聚文化学者、技术专家与传承人的专业团队</p>
          </div>

          <div class="team-grid">
            <div class="team-member">
              <div class="member-avatar">
                <img src="http://localhost:3000/images/team/member1.jpg" alt="文化研究专家" />
              </div>
              <div class="member-info">
                <h4>李文华</h4>
                <p class="member-role">文化研究专家</p>
                <p class="member-desc">专注于非遗文化研究20余年，主持多项国家级非遗保护项目。</p>
              </div>
            </div>

            <div class="team-member">
              <div class="member-avatar">
                <img src="http://localhost:3000/images/team/member2.jpg" alt="技术总监" />
              </div>
              <div class="member-info">
                <h4>张明轩</h4>
                <p class="member-role">技术总监</p>
                <p class="member-desc">致力于文化遗产数字化技术研发，推动传统文化与现代科技融合。</p>
              </div>
            </div>

            <div class="team-member">
              <div class="member-avatar">
                <img src="http://localhost:3000/images/team/member3.jpg" alt="非遗传承人" />
              </div>
              <div class="member-info">
                <h4>王秀兰</h4>
                <p class="member-role">非遗传承人</p>
                <p class="member-desc">国家级非物质文化遗产传承人，精通传统手工艺制作技艺。</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 发展历程 -->
      <section class="timeline-section">
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">发展历程</h2>
            <p class="section-subtitle">见证我们在非遗保护道路上的每一个重要时刻</p>
          </div>

          <div class="timeline" ref="timeline">
            <div class="timeline-item"
                 v-for="(item, index) in timelineItems"
                 :key="index"
                 :class="{ 'animate-in': item.isVisible }"
                 :style="{ animationDelay: `${index * 0.2}s` }">
              <div class="timeline-year">{{ item.year }}</div>
              <div class="timeline-content">
                <h4>{{ item.title }}</h4>
                <p>{{ item.description }}</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 联系我们 -->
      <section class="contact-section">
        <div class="container">
          <div class="contact-content">
            <div class="contact-info">
              <h3>联系我们</h3>
              <p>如果您对非遗文化保护有任何想法或建议，欢迎与我们联系</p>

              <div class="contact-details">
                <div class="contact-item">
                  <i class="contact-icon">📧</i>
                  <span><EMAIL></span>
                </div>
                <div class="contact-item">
                  <i class="contact-icon">📞</i>
                  <span>400-888-9999</span>
                </div>
                <div class="contact-item">
                  <i class="contact-icon">📍</i>
                  <span>北京市朝阳区文化创意产业园</span>
                </div>
              </div>
            </div>

            <div class="contact-form">
              <h4>留言咨询</h4>
              <form @submit.prevent="submitForm">
                <div class="form-group">
                  <input type="text" v-model="form.name" placeholder="您的姓名" required>
                </div>
                <div class="form-group">
                  <input type="email" v-model="form.email" placeholder="邮箱地址" required>
                </div>
                <div class="form-group">
                  <textarea v-model="form.message" placeholder="留言内容" rows="4" required></textarea>
                </div>
                <button type="submit" class="submit-btn">发送留言</button>
              </form>
            </div>
          </div>
        </div>
      </section>
    </main>
  </div>
</template>

<script>
import Navbar from '@/components/common/Navbar.vue'

export default {
  name: 'About',
  components: {
    Navbar
  },
  data() {
    return {
      form: {
        name: '',
        email: '',
        message: ''
      },
      timelineItems: [
        {
          year: '2020',
          title: '项目启动',
          description: '正式启动非遗文化数字化保护项目，开始系统性收集整理各地非遗资源。',
          isVisible: false
        },
        {
          year: '2021',
          title: '平台建设',
          description: '完成非遗文化展示平台的基础建设，上线首批精品非遗作品展示。',
          isVisible: false
        },
        {
          year: '2022',
          title: '合作拓展',
          description: '与多家博物馆、文化机构建立合作关系，扩大非遗文化传播影响力。',
          isVisible: false
        },
        {
          year: '2023',
          title: '技术创新',
          description: '引入VR/AR技术，为用户提供沉浸式非遗文化体验，获得广泛好评。',
          isVisible: false
        }
      ]
    }
  },
  mounted() {
    this.setupScrollAnimation()
  },
  methods: {
    submitForm() {
      // 这里可以添加表单提交逻辑
      alert('感谢您的留言，我们会尽快回复！')
      // 重置表单
      this.form = {
        name: '',
        email: '',
        message: ''
      }
    },
    setupScrollAnimation() {
      // 创建 Intersection Observer 来监听时间线项目的可见性
      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              const index = parseInt(entry.target.dataset.index)
              if (index !== undefined && this.timelineItems[index]) {
                this.timelineItems[index].isVisible = true
              }
            }
          })
        },
        {
          threshold: 0.3, // 当元素30%可见时触发
          rootMargin: '0px 0px -50px 0px' // 提前50px触发
        }
      )

      // 观察所有时间线项目
      this.$nextTick(() => {
        const timelineItems = document.querySelectorAll('.timeline-item')
        timelineItems.forEach((item, index) => {
          item.dataset.index = index
          observer.observe(item)
        })
      })
    }
  },
  beforeUnmount() {
    // 清理观察器
    if (this.observer) {
      this.observer.disconnect()
    }
  }
}
</script>

<style scoped>
.about-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f4f1ed 0%, #e8e0d6 100%);
}

.main-content {
  padding-top: 70px; /* 为固定导航栏留出空间 */
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 英雄区域 */
.hero-section {
  background: linear-gradient(135deg, #a0896b 0%, #8b7355 100%);
  color: white;
  padding: 100px 0;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
}

.hero-content {
  position: relative;
  z-index: 1;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.hero-subtitle {
  font-size: 1.3rem;
  margin-bottom: 30px;
  opacity: 0.9;
}

.hero-decoration {
  width: 100px;
  height: 4px;
  background: linear-gradient(90deg, transparent, white, transparent);
  margin: 0 auto;
  border-radius: 2px;
}

/* 通用区域样式 */
.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #5d4e37;
  margin-bottom: 15px;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(135deg, #a0896b 0%, #8b7355 100%);
  border-radius: 2px;
}

.section-subtitle {
  font-size: 1.1rem;
  color: #8b7355;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* 使命区域 */
.mission-section {
  padding: 100px 0;
  background: #faf8f5;
}

.mission-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
  margin-top: 60px;
}

.mission-card {
  background: white;
  padding: 40px 30px;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(139, 115, 85, 0.15);
  transition: all 0.3s ease;
  border: 1px solid rgba(160, 137, 107, 0.2);
}

.mission-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(139, 115, 85, 0.25);
  border-color: rgba(160, 137, 107, 0.4);
}

.mission-icon {
  font-size: 3rem;
  margin-bottom: 20px;
}

.mission-card h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #5d4e37;
  margin-bottom: 15px;
}

.mission-card p {
  color: #8b7355;
  line-height: 1.6;
  font-size: 1rem;
}

/* 团队区域 */
.team-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #f0ebe4 0%, #e8ddd1 100%);
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 40px;
  margin-top: 60px;
}

.team-member {
  background: white;
  padding: 30px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 25px;
  box-shadow: 0 10px 30px rgba(139, 115, 85, 0.15);
  transition: all 0.3s ease;
  border: 1px solid rgba(160, 137, 107, 0.1);
}

.team-member:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(139, 115, 85, 0.25);
}

.member-avatar {
  flex-shrink: 0;
}

.member-avatar img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #a0896b;
}

.member-info h4 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #5d4e37;
  margin-bottom: 5px;
}

.member-role {
  color: #a0896b;
  font-weight: 500;
  margin-bottom: 10px;
}

.member-desc {
  color: #8b7355;
  font-size: 0.95rem;
  line-height: 1.5;
}

/* 时间线区域 */
.timeline-section {
  padding: 100px 0;
  background: #faf8f5;
}

.timeline {
  position: relative;
  max-width: 800px;
  margin: 60px auto 0;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(135deg, #a0896b 0%, #8b7355 100%);
  transform: translateX(-50%);
  animation: timelineGrow 2s ease-out;
}

/* 时间线生长动画 */
@keyframes timelineGrow {
  from {
    height: 0;
  }
  to {
    height: 100%;
  }
}

.timeline-item {
  position: relative;
  margin-bottom: 50px;
  display: flex;
  align-items: center;
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 动画进入状态 */
.timeline-item.animate-in {
  opacity: 1;
  transform: translateY(0);
}

.timeline-item:nth-child(odd) {
  flex-direction: row;
}

.timeline-item:nth-child(even) {
  flex-direction: row-reverse;
}

/* 奇数项从左侧滑入 */
.timeline-item:nth-child(odd):not(.animate-in) {
  transform: translateX(-100px) translateY(50px);
}

/* 偶数项从右侧滑入 */
.timeline-item:nth-child(even):not(.animate-in) {
  transform: translateX(100px) translateY(50px);
}

.timeline-year {
  background: linear-gradient(135deg, #a0896b 0%, #8b7355 100%);
  color: white;
  padding: 15px 25px;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1.1rem;
  position: relative;
  z-index: 2;
  box-shadow: 0 5px 15px rgba(160, 137, 107, 0.4);
  transition: all 0.3s ease;
}

/* 年份悬停效果 */
.timeline-year:hover {
  transform: scale(1.1);
  box-shadow: 0 8px 25px rgba(160, 137, 107, 0.6);
}

/* 年份脉冲动画 */
.timeline-item.animate-in .timeline-year {
  animation: pulse 2s ease-in-out;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.timeline-content {
  background: white;
  padding: 25px 30px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(139, 115, 85, 0.15);
  margin: 0 30px;
  flex: 1;
  border: 1px solid rgba(160, 137, 107, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

/* 内容卡片悬停效果 */
.timeline-content:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(139, 115, 85, 0.25);
  border-color: rgba(160, 137, 107, 0.4);
}

/* 内容卡片光泽效果 */
.timeline-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  transition: left 0.5s ease;
}

.timeline-item.animate-in .timeline-content::before {
  left: 100%;
}

.timeline-content h4 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #5d4e37;
  margin-bottom: 10px;
  position: relative;
  z-index: 1;
}

.timeline-content p {
  color: #8b7355;
  line-height: 1.6;
  position: relative;
  z-index: 1;
}

/* 联系区域 */
.contact-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #5d4e37 0%, #4a3d2a 100%);
  color: white;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: start;
}

.contact-info h3 {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 20px;
}

.contact-info p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin-bottom: 40px;
  line-height: 1.6;
}

.contact-details {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 1rem;
}

.contact-icon {
  font-size: 1.2rem;
}

.contact-form {
  background: rgba(255,255,255,0.1);
  padding: 40px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.2);
}

.contact-form h4 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 30px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 15px 20px;
  border: none;
  border-radius: 10px;
  background: rgba(255,255,255,0.9);
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  background: white;
  box-shadow: 0 0 0 3px rgba(160, 137, 107, 0.4);
}

.submit-btn {
  background: linear-gradient(135deg, #a0896b 0%, #8b7355 100%);
  color: white;
  border: none;
  padding: 15px 40px;
  border-radius: 50px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(160, 137, 107, 0.5);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .mission-grid,
  .team-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .team-member {
    flex-direction: column;
    text-align: center;
  }

  .timeline::before {
    left: 30px;
  }

  .timeline-item {
    flex-direction: row !important;
    padding-left: 60px;
  }

  /* 移动端动画调整 */
  .timeline-item:nth-child(odd):not(.animate-in),
  .timeline-item:nth-child(even):not(.animate-in) {
    transform: translateX(0) translateY(50px);
  }

  .timeline-year {
    position: absolute;
    left: 0;
    padding: 10px 15px;
    font-size: 0.9rem;
  }

  .timeline-content {
    margin: 0;
  }

  .contact-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .contact-form {
    padding: 30px 20px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 15px;
  }

  .hero-section {
    padding: 60px 0;
  }

  .mission-section,
  .team-section,
  .timeline-section,
  .contact-section {
    padding: 60px 0;
  }

  .mission-card,
  .contact-form {
    padding: 25px 20px;
  }

  .team-member {
    padding: 25px 20px;
  }
}
</style>