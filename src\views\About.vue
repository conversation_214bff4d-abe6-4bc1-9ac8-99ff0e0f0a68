<template>
  <div class="about-page">
    <!-- 导航栏 -->
    <Navbar />

    <!-- 主要内容 -->
    <main class="main-content">
      <!-- 英雄区域 -->
      <section class="hero-section">
        <div class="hero-content">
          <h1 class="hero-title">关于我们</h1>
          <p class="hero-subtitle">传承千年文化，守护非遗之美</p>
          <div class="hero-decoration"></div>
        </div>
      </section>

      <!-- 使命愿景 -->
      <section class="mission-section">
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">我们的使命</h2>
            <p class="section-subtitle">致力于非物质文化遗产的保护、传承与创新发展</p>
          </div>

          <div class="mission-grid">
            <div class="mission-card">
              <div class="mission-icon">
                <i class="icon-protect">🛡️</i>
              </div>
              <h3>保护传承</h3>
              <p>深入挖掘和记录非遗文化的精髓，运用现代技术手段进行数字化保护，确保珍贵文化遗产得以完整传承。</p>
            </div>

            <div class="mission-card">
              <div class="mission-icon">
                <i class="icon-innovate">💡</i>
              </div>
              <h3>创新发展</h3>
              <p>在尊重传统的基础上，探索非遗文化与现代生活的融合方式，让古老的智慧在新时代焕发生机。</p>
            </div>

            <div class="mission-card">
              <div class="mission-icon">
                <i class="icon-share">🌍</i>
              </div>
              <h3>文化传播</h3>
              <p>通过多元化的展示平台和互动体验，让更多人了解、欣赏和参与非遗文化的传承与发展。</p>
            </div>
          </div>
        </div>
      </section>

      <!-- 团队介绍 -->
      <section class="team-section">
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">核心团队</h2>
            <p class="section-subtitle">汇聚文化学者、技术专家与传承人的专业团队</p>
          </div>

          <div class="team-grid">
            <div class="team-member">
              <div class="member-avatar">
                <img src="http://localhost:3000/images/team/member1.jpg" alt="文化研究专家" />
              </div>
              <div class="member-info">
                <h4>李文华</h4>
                <p class="member-role">文化研究专家</p>
                <p class="member-desc">专注于非遗文化研究20余年，主持多项国家级非遗保护项目。</p>
              </div>
            </div>

            <div class="team-member">
              <div class="member-avatar">
                <img src="http://localhost:3000/images/team/member2.jpg" alt="技术总监" />
              </div>
              <div class="member-info">
                <h4>张明轩</h4>
                <p class="member-role">技术总监</p>
                <p class="member-desc">致力于文化遗产数字化技术研发，推动传统文化与现代科技融合。</p>
              </div>
            </div>

            <div class="team-member">
              <div class="member-avatar">
                <img src="http://localhost:3000/images/team/member3.jpg" alt="非遗传承人" />
              </div>
              <div class="member-info">
                <h4>王秀兰</h4>
                <p class="member-role">非遗传承人</p>
                <p class="member-desc">国家级非物质文化遗产传承人，精通传统手工艺制作技艺。</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 发展历程 -->
      <section class="timeline-section">
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">发展历程</h2>
            <p class="section-subtitle">见证我们在非遗保护道路上的每一个重要时刻</p>
          </div>

          <div class="timeline">
            <div class="timeline-item">
              <div class="timeline-year">2020</div>
              <div class="timeline-content">
                <h4>项目启动</h4>
                <p>正式启动非遗文化数字化保护项目，开始系统性收集整理各地非遗资源。</p>
              </div>
            </div>

            <div class="timeline-item">
              <div class="timeline-year">2021</div>
              <div class="timeline-content">
                <h4>平台建设</h4>
                <p>完成非遗文化展示平台的基础建设，上线首批精品非遗作品展示。</p>
              </div>
            </div>

            <div class="timeline-item">
              <div class="timeline-year">2022</div>
              <div class="timeline-content">
                <h4>合作拓展</h4>
                <p>与多家博物馆、文化机构建立合作关系，扩大非遗文化传播影响力。</p>
              </div>
            </div>

            <div class="timeline-item">
              <div class="timeline-year">2023</div>
              <div class="timeline-content">
                <h4>技术创新</h4>
                <p>引入VR/AR技术，为用户提供沉浸式非遗文化体验，获得广泛好评。</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 联系我们 -->
      <section class="contact-section">
        <div class="container">
          <div class="contact-content">
            <div class="contact-info">
              <h3>联系我们</h3>
              <p>如果您对非遗文化保护有任何想法或建议，欢迎与我们联系</p>

              <div class="contact-details">
                <div class="contact-item">
                  <i class="contact-icon">📧</i>
                  <span><EMAIL></span>
                </div>
                <div class="contact-item">
                  <i class="contact-icon">📞</i>
                  <span>400-888-9999</span>
                </div>
                <div class="contact-item">
                  <i class="contact-icon">📍</i>
                  <span>北京市朝阳区文化创意产业园</span>
                </div>
              </div>
            </div>

            <div class="contact-form">
              <h4>留言咨询</h4>
              <form @submit.prevent="submitForm">
                <div class="form-group">
                  <input type="text" v-model="form.name" placeholder="您的姓名" required>
                </div>
                <div class="form-group">
                  <input type="email" v-model="form.email" placeholder="邮箱地址" required>
                </div>
                <div class="form-group">
                  <textarea v-model="form.message" placeholder="留言内容" rows="4" required></textarea>
                </div>
                <button type="submit" class="submit-btn">发送留言</button>
              </form>
            </div>
          </div>
        </div>
      </section>
    </main>
  </div>
</template>

<script>
import Navbar from '@/components/common/Navbar.vue'

export default {
  name: 'About',
  components: {
    Navbar
  },
  data() {
    return {
      form: {
        name: '',
        email: '',
        message: ''
      }
    }
  },
  methods: {
    submitForm() {
      // 这里可以添加表单提交逻辑
      alert('感谢您的留言，我们会尽快回复！')
      // 重置表单
      this.form = {
        name: '',
        email: '',
        message: ''
      }
    }
  }
}
</script>

<style scoped>
.about-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.main-content {
  padding-top: 70px; /* 为固定导航栏留出空间 */
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 英雄区域 */
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 100px 0;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
}

.hero-content {
  position: relative;
  z-index: 1;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.hero-subtitle {
  font-size: 1.3rem;
  margin-bottom: 30px;
  opacity: 0.9;
}

.hero-decoration {
  width: 100px;
  height: 4px;
  background: linear-gradient(90deg, transparent, white, transparent);
  margin: 0 auto;
  border-radius: 2px;
}

/* 通用区域样式 */
.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 15px;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
}

.section-subtitle {
  font-size: 1.1rem;
  color: #7f8c8d;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* 使命区域 */
.mission-section {
  padding: 100px 0;
  background: white;
}

.mission-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
  margin-top: 60px;
}

.mission-card {
  background: white;
  padding: 40px 30px;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.mission-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.mission-icon {
  font-size: 3rem;
  margin-bottom: 20px;
}

.mission-card h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 15px;
}

.mission-card p {
  color: #7f8c8d;
  line-height: 1.6;
  font-size: 1rem;
}

/* 团队区域 */
.team-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 40px;
  margin-top: 60px;
}

.team-member {
  background: white;
  padding: 30px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 25px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.team-member:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.member-avatar {
  flex-shrink: 0;
}

.member-avatar img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #667eea;
}

.member-info h4 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 5px;
}

.member-role {
  color: #667eea;
  font-weight: 500;
  margin-bottom: 10px;
}

.member-desc {
  color: #7f8c8d;
  font-size: 0.95rem;
  line-height: 1.5;
}

/* 时间线区域 */
.timeline-section {
  padding: 100px 0;
  background: white;
}

.timeline {
  position: relative;
  max-width: 800px;
  margin: 60px auto 0;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transform: translateX(-50%);
}

.timeline-item {
  position: relative;
  margin-bottom: 50px;
  display: flex;
  align-items: center;
}

.timeline-item:nth-child(odd) {
  flex-direction: row;
}

.timeline-item:nth-child(even) {
  flex-direction: row-reverse;
}

.timeline-year {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 15px 25px;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1.1rem;
  position: relative;
  z-index: 2;
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.timeline-content {
  background: white;
  padding: 25px 30px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  margin: 0 30px;
  flex: 1;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.timeline-content h4 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 10px;
}

.timeline-content p {
  color: #7f8c8d;
  line-height: 1.6;
}

/* 联系区域 */
.contact-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: start;
}

.contact-info h3 {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 20px;
}

.contact-info p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin-bottom: 40px;
  line-height: 1.6;
}

.contact-details {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 1rem;
}

.contact-icon {
  font-size: 1.2rem;
}

.contact-form {
  background: rgba(255,255,255,0.1);
  padding: 40px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.2);
}

.contact-form h4 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 30px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 15px 20px;
  border: none;
  border-radius: 10px;
  background: rgba(255,255,255,0.9);
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
}

.submit-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 15px 40px;
  border-radius: 50px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .mission-grid,
  .team-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .team-member {
    flex-direction: column;
    text-align: center;
  }

  .timeline::before {
    left: 30px;
  }

  .timeline-item {
    flex-direction: row !important;
    padding-left: 60px;
  }

  .timeline-year {
    position: absolute;
    left: 0;
    padding: 10px 15px;
    font-size: 0.9rem;
  }

  .timeline-content {
    margin: 0;
  }

  .contact-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .contact-form {
    padding: 30px 20px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 15px;
  }

  .hero-section {
    padding: 60px 0;
  }

  .mission-section,
  .team-section,
  .timeline-section,
  .contact-section {
    padding: 60px 0;
  }

  .mission-card,
  .contact-form {
    padding: 25px 20px;
  }

  .team-member {
    padding: 25px 20px;
  }
}
</style>