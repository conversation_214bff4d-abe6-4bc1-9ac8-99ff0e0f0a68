<template>
  <section class="masters-section">
    <div class="section-container">
      <h2 class="section-title">非遗传承人</h2>
      <div class="masters-grid">
        <div class="master-card" v-for="master in masters" :key="master.id">
          <img :src="master.avatar" :alt="master.name" />
          <h3>{{ master.name }}</h3>
          <p class="master-title">{{ master.title }}</p>
          <p>{{ master.specialty }}</p>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref } from 'vue'

const masters = ref([
  {
    id: 1,
    name: '张大师',
    title: '国家级非遗传承人',
    specialty: '景泰蓝制作技艺',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face'
  },
  {
    id: 2,
    name: '李绣娘',
    title: '省级非遗传承人',
    specialty: '苏绣技艺',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face'
  },
  {
    id: 3,
    name: '王艺术家',
    title: '京剧表演艺术家',
    specialty: '京剧表演',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'
  }
])
</script>

<style scoped>
.masters-section {
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80px 0;
  box-sizing: border-box;
}

.section-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-title {
  font-size: 3rem;
  text-align: center;
  margin-bottom: 60px;
  color: white;
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.masters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 30px;
  max-height: 60vh;
  overflow-y: auto;
}

.master-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 35px;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.master-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.3);
}

.master-card img {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  margin-bottom: 20px;
  object-fit: cover;
  border: 3px solid rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.master-card:hover img {
  border-color: rgba(102, 126, 234, 0.6);
  transform: scale(1.05);
}

.master-card h3 {
  font-size: 1.4rem;
  margin-bottom: 12px;
  color: #2c3e50;
  font-weight: 600;
}

.master-title {
  color: #667eea;
  font-weight: 600;
  margin-bottom: 10px;
  font-size: 0.95rem;
}

.master-card p:last-child {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .masters-section {
    padding: 60px 0;
  }
  
  .section-title {
    font-size: 2.5rem;
    margin-bottom: 40px;
  }
  
  .masters-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    max-height: 65vh;
  }
  
  .master-card {
    padding: 30px;
  }
  
  .master-card img {
    width: 90px;
    height: 90px;
  }
  
  .master-card h3 {
    font-size: 1.3rem;
  }
}

@media (max-width: 480px) {
  .section-container {
    padding: 0 15px;
  }
  
  .section-title {
    font-size: 2rem;
  }
  
  .masters-grid {
    grid-template-columns: 1fr;
    max-height: 70vh;
  }
  
  .master-card {
    padding: 25px;
  }
  
  .master-card img {
    width: 80px;
    height: 80px;
  }
}
</style>