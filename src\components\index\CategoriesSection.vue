<template>
  <section class="categories-section">
    <div class="section-container">
      <h2 class="section-title">非遗分类</h2>
      <div class="categories-grid">
        <div class="category-card" v-for="category in categories" :key="category.id">
          <div class="category-icon">{{ category.icon }}</div>
          <h3>{{ category.name }}</h3>
          <p>{{ category.description }}</p>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref } from 'vue'

const categories = ref([
  {
    id: 1,
    name: '传统技艺',
    icon: '🎨',
    description: '传统手工艺制作技艺'
  },
  {
    id: 2,
    name: '传统音乐',
    icon: '🎵',
    description: '民族音乐与乐器制作'
  },
  {
    id: 3,
    name: '传统舞蹈',
    icon: '💃',
    description: '民族舞蹈与表演艺术'
  },
  {
    id: 4,
    name: '传统戏剧',
    icon: '🎭',
    description: '戏曲与曲艺表演'
  },
  {
    id: 5,
    name: '传统医药',
    icon: '🌿',
    description: '中医药与养生文化'
  },
  {
    id: 6,
    name: '民俗活动',
    icon: '🏮',
    description: '传统节日与习俗'
  }
])
</script>

<style scoped>
.categories-section {
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80px 0;
  box-sizing: border-box;
}

.section-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-title {
  font-size: 3rem;
  text-align: center;
  margin-bottom: 60px;
  color: #2c3e50;
  font-weight: 700;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
  max-height: 60vh;
  overflow-y: auto;
}

.category-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  padding: 30px 25px;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.category-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 0.95);
}

.category-icon {
  font-size: 3.5rem;
  margin-bottom: 20px;
  display: block;
}

.category-card h3 {
  font-size: 1.4rem;
  margin-bottom: 12px;
  color: #2c3e50;
  font-weight: 600;
}

.category-card p {
  color: #666;
  line-height: 1.6;
  font-size: 0.95rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .categories-section {
    padding: 60px 0;
  }
  
  .section-title {
    font-size: 2.5rem;
    margin-bottom: 40px;
  }
  
  .categories-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    max-height: 65vh;
  }
  
  .category-card {
    padding: 25px 20px;
  }
  
  .category-icon {
    font-size: 3rem;
  }
  
  .category-card h3 {
    font-size: 1.3rem;
  }
}

@media (max-width: 480px) {
  .section-container {
    padding: 0 15px;
  }
  
  .section-title {
    font-size: 2rem;
  }
  
  .categories-grid {
    grid-template-columns: 1fr;
    max-height: 70vh;
  }
}
</style>