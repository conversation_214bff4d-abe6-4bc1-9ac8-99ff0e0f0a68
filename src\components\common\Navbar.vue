<template>
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <img src="http://localhost:3000/images/图标/logo.png" alt="logo" class="nav-logo-img">
                <span class="nav-brand">非遗文化</span>
            </div>

            <ul class="nav-menu" :class="{ active: isMenuOpen }">
                <li class="nav-item">
                    <router-link to="/" class="nav-link" @click="closeMenu">首页</router-link>
                </li>
                <li class="nav-item">
                    <router-link to="/heritage" class="nav-link" @click="closeMenu">非遗探索</router-link>
                </li>
                <li class="nav-item">
                    <router-link to="/culture" class="nav-link" @click="closeMenu">文化传承</router-link>
                </li>
                <li class="nav-item">
                    <router-link to="/gallery" class="nav-link" @click="closeMenu">作品展示</router-link>
                </li>
                <li class="nav-item">
                    <router-link to="/about" class="nav-link" @click="closeMenu">关于我们</router-link>
                </li>
            </ul>

            <div class="nav-toggle" :class="{ active: isMenuOpen }" @click="toggleMenu">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>
</template>

<script>
export default {
    name: 'Navbar',
    data() {
        return {
            isMenuOpen: false
        }
    },
    methods: {
        toggleMenu() {
            this.isMenuOpen = !this.isMenuOpen
        },
        closeMenu() {
            this.isMenuOpen = false
        }
    }
}
</script>
<style scoped>
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.nav-logo-img {
    height: 40px;
    width: auto;
    object-fit: contain;
}

.nav-brand {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 30px;
}

.nav-item {
    position: relative;
}

.nav-link {
    text-decoration: none;
    color: #2c3e50;
    font-weight: 500;
    font-size: 1rem;
    padding: 8px 16px;
    border-radius: 25px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

.nav-link.router-link-active {
    color: white;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 5px;
}

.bar {
    width: 25px;
    height: 3px;
    background: #2c3e50;
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 2px;
}

/* 移动端导航样式 */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(10px);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 20px 0;
        gap: 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-item {
        margin: 10px 0;
    }

    .nav-link {
        padding: 15px 20px;
        display: block;
        font-size: 1.1rem;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .nav-toggle.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }
}
</style>